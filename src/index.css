@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Design System Variables */
:root {
  --color-deep-teal: #004E64;
  --color-coral: #FF6B35;
  --color-coral-hover: #E0562A;
  --color-cool-gray: #D6DBDC;
  --color-light-bg: #FAFAFA;
  --color-text-gray: #999;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom button animations */
.btn-coral {
  @apply bg-[#FF6B35] text-white font-bold rounded-lg transition-all duration-300 shadow-xl;
}

.btn-coral:hover {
  @apply bg-[#E0562A] transform scale-105;
}

/* Form focus styles */
.form-input {
  @apply w-full px-5 py-4 border-2 border-gray-300 rounded-lg text-lg transition-all duration-300;
}

.form-input:focus {
  @apply ring-2 ring-[#FF6B35] border-[#FF6B35] outline-none;
}

.form-label {
  @apply block text-base font-semibold text-[#004E64] mb-3;
}

/* Smooth page transitions */
.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
