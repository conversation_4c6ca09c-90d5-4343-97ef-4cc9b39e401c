import React from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Send, Phone, Mail, MessageSquare, User } from 'lucide-react';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  caseType: string;
  message: string;
}

const ConsultationForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<FormData>();

  const onSubmit = (data: FormData) => {
    console.log('Form submitted:', data);
    // Handle form submission here
    alert('Thank you for your submission! We will contact you within 24 hours.');
    reset();
  };

  const caseTypes = [
    'Car Accident',
    'Truck Accident', 
    'Slip & Fall',
    'Medical Malpractice',
    'Wrongful Death',
    'Other Personal Injury'
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
      className="bg-[#FAFAFA] rounded-2xl shadow-2xl p-10 border border-[#D6DBDC]"
    >
      <div className="mb-10 text-center">
        <h3 className="text-3xl font-bold text-[#004E64] mb-4">Get Your Free Case Review</h3>
        <p className="text-[#999] text-lg">No obligation. Confidential consultation with experienced attorneys.</p>
        <div className="flex justify-center mt-4">
          <div className="bg-[#FF6B35]/10 text-[#FF6B35] px-4 py-2 rounded-full text-sm font-semibold border border-[#FF6B35]/20">
            ✓ 100% Free & Confidential
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              <User className="inline h-4 w-4 mr-1" />
              First Name *
            </label>
            <input
              type="text"
              {...register('firstName', { required: 'First name is required' })}
              className="form-input"
              placeholder="Your first name"
            />
            {errors.firstName && (
              <p className="mt-2 text-sm text-red-600">{errors.firstName.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              Last Name *
            </label>
            <input
              type="text"
              {...register('lastName', { required: 'Last name is required' })}
              className="form-input"
              placeholder="Your last name"
            />
            {errors.lastName && (
              <p className="mt-2 text-sm text-red-600">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        {/* Contact Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              <Mail className="inline h-4 w-4 mr-1" />
              Email Address *
            </label>
            <input
              type="email"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Invalid email address'
                }
              })}
              className="form-input"
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="mt-2 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              <Phone className="inline h-4 w-4 mr-1" />
              Phone Number *
            </label>
            <input
              type="tel"
              {...register('phone', { required: 'Phone number is required' })}
              className="form-input"
              placeholder="(*************"
            />
            {errors.phone && (
              <p className="mt-2 text-sm text-red-600">{errors.phone.message}</p>
            )}
          </div>
        </div>

        {/* Case Type */}
        <div>
          <label className="form-label">
            Type of Case *
          </label>
          <select
            {...register('caseType', { required: 'Please select a case type' })}
            className="form-input"
          >
            <option value="">Select your case type</option>
            {caseTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
          {errors.caseType && (
            <p className="mt-2 text-sm text-red-600">{errors.caseType.message}</p>
          )}
        </div>

        {/* Message */}
        <div>
          <label className="form-label">
            <MessageSquare className="inline h-4 w-4 mr-1" />
            Tell us about your case
          </label>
          <textarea
            rows={5}
            {...register('message')}
            className="form-input resize-none"
            placeholder="Please provide details about your accident and injuries..."
          />
        </div>

        {/* Submit Button */}
        <motion.button
          type="submit"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="btn-coral w-full py-5 px-8 text-xl flex items-center justify-center space-x-3"
        >
          <Send className="h-6 w-6" />
          <span>Get My Free Case Review</span>
        </motion.button>

        {/* Disclaimer */}
        <p className="text-sm text-[#999] text-center leading-relaxed">
          By submitting this form, you agree to be contacted by our office.
          This does not create an attorney-client relationship. All information is confidential.
        </p>
      </form>
    </motion.div>
  );
};

export default ConsultationForm;