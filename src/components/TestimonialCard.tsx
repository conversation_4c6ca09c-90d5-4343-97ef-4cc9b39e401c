import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

interface TestimonialCardProps {
  name: string;
  location: string;
  rating: number;
  testimonial: string;
  caseType: string;
  index: number;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({
  name,
  location,
  rating,
  testimonial,
  caseType,
  index
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 relative"
    >
      {/* Quote Icon */}
      <div className="absolute top-6 right-6">
        <Quote className="h-8 w-8 text-amber-200" fill="currentColor" />
      </div>

      {/* Rating Stars */}
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-5 w-5 ${
              i < rating ? 'text-amber-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>

      {/* Testimonial Text */}
      <blockquote className="text-gray-700 leading-relaxed mb-6 text-lg italic">
        "{testimonial}"
      </blockquote>

      {/* Client Info */}
      <div className="border-t border-gray-100 pt-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="font-semibold text-gray-900">{name}</p>
            <p className="text-sm text-gray-600">{location}</p>
          </div>
          <div className="text-right">
            <span className="inline-block bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full font-medium">
              {caseType}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TestimonialCard;