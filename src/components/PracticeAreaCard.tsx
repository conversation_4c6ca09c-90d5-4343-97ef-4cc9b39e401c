import React from 'react';
import { motion } from 'framer-motion';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface PracticeAreaCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  index: number;
}

const PracticeAreaCard: React.FC<PracticeAreaCardProps> = ({ icon: Icon, title, description, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 group border border-gray-100 transform hover:-translate-y-2"
    >
      <div className="mb-6">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-amber-100 rounded-full group-hover:bg-amber-200 transition-colors duration-300">
          <Icon className="h-10 w-10 text-amber-600" />
        </div>
      </div>
      
      <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-800 transition-colors duration-300">
        {title}
      </h3>
      
      <p className="text-gray-600 leading-relaxed mb-6 text-lg">
        {description}
      </p>
      
      <div className="flex items-center text-blue-800 font-bold group-hover:text-amber-600 transition-colors duration-300 text-lg">
        <span>Learn More</span>
        <motion.span
          initial={{ x: 0 }}
          whileHover={{ x: 5 }}
          className="ml-2"
        >
          →
        </motion.span>
      </div>
    </motion.div>
  );
};

export default PracticeAreaCard;