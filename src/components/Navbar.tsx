import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Scale } from 'lucide-react';
import { motion } from 'framer-motion';

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'Practice Areas', path: '/practice-areas' },
    { name: 'Testimonials', path: '/testimonials' },
    { name: 'Blog', path: '/blog' },
    { name: 'Contact', path: '/contact' },
  ];

  return (
    <nav className={`fixed w-full z-50 transition-all duration-500 ${
      isScrolled ? 'bg-white shadow-xl py-2' : 'bg-white/95 backdrop-blur-sm py-4'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Scale className="h-8 w-8 text-blue-800" />
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-blue-800">Florida Accident Lawyer</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`text-gray-700 hover:text-blue-800 transition-colors duration-200 font-medium ${
                  location.pathname === link.path ? 'text-blue-800 font-bold border-b-2 border-blue-800' : ''
                }`}
              >
                {link.name}
              </Link>
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden lg:flex items-center space-x-6">
            <a href="tel:+15551234567" className="flex items-center space-x-2 text-blue-800 hover:text-blue-900 font-bold text-lg">
              <Phone className="h-4 w-4" />
              <span className="font-semibold">(*************</span>
            </a>
            <Link
              to="/free-case-review"
              className="bg-amber-600 text-white px-8 py-3 rounded-lg font-bold hover:bg-amber-700 transition-all duration-200 shadow-lg transform hover:scale-105"
            >
              Free Case Review
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="lg:hidden p-2"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: isOpen ? 1 : 0, height: isOpen ? 'auto' : 0 }}
        transition={{ duration: 0.3 }}
        className="lg:hidden bg-white border-t"
        style={{ overflow: 'hidden' }}
      >
        <div className="px-4 py-4 space-y-4">
          {navLinks.map((link) => (
            <Link
              key={link.path}
              to={link.path}
              onClick={() => setIsOpen(false)}
              className={`block text-gray-700 hover:text-blue-800 transition-colors duration-200 ${
                location.pathname === link.path ? 'text-blue-800 font-semibold' : ''
              }`}
            >
              {link.name}
            </Link>
          ))}
          <div className="pt-4 border-t">
            <a href="tel:+1234567890" className="flex items-center space-x-2 text-blue-800 mb-4">
              <Phone className="h-4 w-4" />
              <span className="font-semibold">(*************</span>
            </a>
            <Link
              to="/free-case-review"
              onClick={() => setIsOpen(false)}
              className="block w-full text-center bg-amber-600 text-white py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
            >
              Free Case Review
            </Link>
          </div>
        </div>
      </motion.div>
    </nav>
  );
};

export default Navbar;