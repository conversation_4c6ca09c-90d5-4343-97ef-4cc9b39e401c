import React from 'react';
import { Link } from 'react-router-dom';
import { Phone, Mail, MapPin, Scale, Clock, Shield, Award } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-slate-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Link to="/" className="flex items-center space-x-2">
              <Scale className="h-8 w-8 text-amber-400" />
              <div className="flex flex-col">
                <span className="text-2xl font-bold">Florida Accident Lawyer</span>
              </div>
            </Link>
            <p className="text-gray-300 leading-relaxed">
              Florida's most trusted personal injury law firm. Fighting for accident victims 
              across the state with over 25 years of experience and $100M+ recovered.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-amber-400">
                <Award className="h-5 w-5" />
                <span className="font-semibold">Licensed in All Florida Courts</span>
              </div>
              <div className="flex items-center space-x-2 text-amber-400">
                <Shield className="h-5 w-5" />
                <span className="font-semibold">No Win, No Fee Guarantee</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-amber-400">Quick Links</h3>
            <ul className="space-y-2">
              {[
                { name: 'About Us', path: '/about' },
                { name: 'Practice Areas', path: '/practice-areas' },
                { name: 'Testimonials', path: '/testimonials' },
                { name: 'Free Case Review', path: '/free-case-review' },
                { name: 'Blog', path: '/blog' },
                { name: 'Contact', path: '/contact' },
              ].map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-gray-300 hover:text-amber-400 transition-colors duration-200 font-medium"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Practice Areas */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-amber-400">Practice Areas</h3>
            <ul className="space-y-2 text-gray-300">
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Car Accidents</li>
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Truck Accidents</li>
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Slip & Fall</li>
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Medical Malpractice</li>
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Wrongful Death</li>
              <li className="hover:text-amber-400 transition-colors duration-200 cursor-pointer font-medium">Personal Injury</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-amber-400">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-amber-400" />
                <div>
                  <a href="tel:+15551234567" className="font-bold text-lg hover:text-amber-400 transition-colors duration-200">(*************</a>
                  <p className="text-sm text-gray-300">24/7 Emergency Line</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-amber-400" />
                <a href="mailto:<EMAIL>" className="hover:text-amber-400 transition-colors duration-200 font-medium"><EMAIL></a>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-amber-400" />
                <p className="font-medium">123 Legal Way, Miami, FL 33101</p>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-amber-400" />
                <div className="font-medium">
                  <p>Mon-Fri: 8AM-6PM</p>
                  <p>Sat-Sun: Emergency Only</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4 text-gray-300">
              <Shield className="h-5 w-5 text-amber-400" />
              <span className="font-medium">Attorney Advertising. Past results do not guarantee future outcomes.</span>
            </div>
            <div className="text-center">
              <Link
                to="/free-case-review"
                className="inline-block bg-amber-600 text-white px-10 py-4 rounded-lg font-bold text-lg hover:bg-amber-700 transition-all duration-200 shadow-lg transform hover:scale-105"
              >
                Get Your Free Case Review
              </Link>
            </div>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; 2024 Florida Accident Lawyer. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;