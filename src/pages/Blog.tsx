import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, User, Clock, ArrowRight, Tag } from 'lucide-react';
import SEO from '../components/SEO';

const Blog: React.FC = () => {
  const blogPosts = [
    {
      id: 1,
      title: '5 Critical Steps to Take After a Car Accident in Florida',
      excerpt: 'Being in a car accident can be overwhelming. Learn the essential steps you need to take to protect yourself legally and financially after an accident.',
      author: '<PERSON>',
      date: '2024-01-15',
      readTime: '8 min read',
      category: 'Car Accidents',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=250&fit=crop'
    },
    {
      id: 2,
      title: 'Understanding Florida\'s No-Fault Insurance Laws',
      excerpt: 'Florida\'s no-fault insurance system can be confusing. We break down what you need to know about PIP coverage and when you can step outside the system.',
      author: '<PERSON>',
      date: '2024-01-12',
      readTime: '6 min read',
      category: 'Insurance',
      image: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=400&h=250&fit=crop'
    },
    {
      id: 3,
      title: 'When to Hire a Personal Injury Attorney: A Complete Guide',
      excerpt: 'Not every injury requires an attorney, but serious cases do. Learn when it\'s time to get professional legal help and what to expect from the process.',
      author: 'Michael Rodriguez',
      date: '2024-01-10',
      readTime: '10 min read',
      category: 'Legal Advice',
      image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=250&fit=crop'
    },
    {
      id: 4,
      title: 'Truck Accident vs Car Accident: Key Legal Differences',
      excerpt: 'Truck accidents involve different laws, regulations, and liability issues than car accidents. Understanding these differences is crucial for your case.',
      author: 'David Wilson',
      date: '2024-01-08',
      readTime: '7 min read',
      category: 'Truck Accidents',
      image: 'https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?w=400&h=250&fit=crop'
    },
    {
      id: 5,
      title: 'Slip and Fall Accidents: Proving Premises Liability in Florida',
      excerpt: 'Property owners have a duty to maintain safe conditions. Learn what evidence you need to prove negligence in a slip and fall case.',
      author: 'Jennifer Davis',
      date: '2024-01-05',
      readTime: '9 min read',
      category: 'Slip & Fall',
      image: 'https://images.unsplash.com/photo-1534384815253-ca145baa95ff?w=400&h=250&fit=crop'
    },
    {
      id: 6,
      title: 'Dealing with Insurance Companies: Common Tactics and How to Respond',
      excerpt: 'Insurance companies use various tactics to minimize payouts. Learn how to recognize these strategies and protect yourself during negotiations.',
      author: 'Michael Rodriguez',
      date: '2024-01-03',
      readTime: '11 min read',
      category: 'Insurance',
      image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop'
    }
  ];

  const categories = ['All', 'Car Accidents', 'Truck Accidents', 'Slip & Fall', 'Insurance', 'Legal Advice'];
  const [selectedCategory, setSelectedCategory] = React.useState('All');

  const filteredPosts = selectedCategory === 'All' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  return (
    <>
      <SEO 
        title="Personal Injury Law Blog - Florida Accident Lawyer"
        description="Read our personal injury law blog for expert advice on car accidents, insurance claims, legal tips, and more. Stay informed with Florida Accident Lawyer."
        keywords="personal injury blog, Florida accident law, car accident tips, insurance advice, legal articles"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-5xl font-bold mb-6">Legal Blog & Resources</h1>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto">
                Stay informed with expert insights, legal tips, and valuable information 
                about personal injury law in Florida.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Category Filter */}
        <section className="py-8 bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex flex-wrap justify-center gap-4"
            >
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-full text-sm font-semibold transition-colors duration-200 ${
                    selectedCategory === category
                      ? 'bg-blue-800 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Featured Post */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Article</h2>
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div className="grid lg:grid-cols-2 gap-0">
                  <div className="relative">
                    <img
                      src={blogPosts[0].image}
                      alt={blogPosts[0].title}
                      className="w-full h-64 lg:h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="inline-block bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Featured
                      </span>
                    </div>
                  </div>
                  <div className="p-8 lg:p-12">
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(blogPosts[0].date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{blogPosts[0].author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{blogPosts[0].readTime}</span>
                      </div>
                    </div>
                    <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                      {blogPosts[0].title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {blogPosts[0].excerpt}
                    </p>
                    <button className="inline-flex items-center bg-blue-800 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-900 transition-colors duration-200">
                      Read Full Article
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Recent Articles</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Expert insights and practical advice from our experienced legal team.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.slice(1).map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="relative">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="inline-flex items-center bg-blue-800 text-white px-3 py-1 rounded-full text-xs font-medium">
                        <Tag className="h-3 w-3 mr-1" />
                        {post.category}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mb-3">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(post.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-xs text-gray-600">{post.author}</span>
                      </div>
                      <button className="inline-flex items-center text-blue-800 font-semibold text-sm hover:text-blue-900 transition-colors duration-200">
                        Read More
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </button>
                    </div>
                  </div>
                </motion.article>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-20 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Stay Informed with Legal Updates</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                Subscribe to our newsletter for the latest personal injury law updates, 
                safety tips, and important legal changes in Florida.
              </p>
              <div className="max-w-md mx-auto flex">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-grow px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
                <button className="bg-amber-600 text-white px-6 py-3 rounded-r-lg font-semibold hover:bg-amber-700 transition-colors duration-200">
                  Subscribe
                </button>
              </div>
              <p className="text-sm text-gray-300 mt-4">
                No spam. Unsubscribe anytime. Your privacy is protected.
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Blog;