import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Clock, Phone, CheckCircle, Award, Users } from 'lucide-react';
import SEO from '../components/SEO';
import ConsultationForm from '../components/ConsultationForm';

const FreeReview: React.FC = () => {
  const benefits = [
    {
      icon: Shield,
      title: 'No Risk, No Obligation',
      description: 'Our consultation is completely free with no strings attached. We only get paid if we win your case.'
    },
    {
      icon: Clock,
      title: '24/7 Availability',
      description: 'Accidents happen at any time. We\'re available around the clock to help when you need us most.'
    },
    {
      icon: Users,
      title: 'Experienced Team',
      description: 'Over 25 years of experience handling personal injury cases with a proven track record of success.'
    },
    {
      icon: Award,
      title: 'Maximum Compensation',
      description: 'We fight aggressively to secure the full compensation you deserve for your injuries and losses.'
    }
  ];

  const whatToExpect = [
    'Free consultation with an experienced attorney',
    'Case evaluation and legal strategy discussion', 
    'Explanation of your rights and options',
    'No upfront costs or hidden fees',
    'Honest assessment of your case\'s value',
    'Timeline and next steps clearly outlined'
  ];

  const stats = [
    { value: '95%', label: 'Success Rate' },
    { value: '$100M+', label: 'Total Recovered' },
    { value: '5,000+', label: 'Clients Helped' },
    { value: '25+', label: 'Years Experience' }
  ];

  return (
    <>
      <SEO 
        title="Free Case Review - Florida Personal Injury Lawyer Consultation"
        description="Get your free personal injury case review from experienced Florida attorneys. No obligation, no upfront fees. Available 24/7 for emergency consultations."
        keywords="free case review, personal injury consultation, Florida accident lawyer, free legal advice, no obligation consultation"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-[#004E64] to-[#003A4D] text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-5xl font-bold mb-6">Get Your Free Case Review</h1>
              <p className="text-xl text-[#D6DBDC] max-w-3xl mx-auto mb-8">
                Don't wait to get the legal help you need. Our experienced attorneys will review your case
                for free and explain your options with no obligation.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.a
                  href="tel:+1234567890"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-coral inline-flex items-center justify-center px-8 py-4 text-lg"
                >
                  <Phone className="h-6 w-6 mr-3" />
                  Call Now: (*************
                </motion.a>
                <motion.a
                  href="#consultation-form"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#004E64] transition-all duration-300 shadow-lg"
                >
                  Fill Out Form Below
                </motion.a>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-4xl font-bold text-[#004E64] mb-2">{stat.value}</div>
                  <div className="text-[#999]">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Free Review Section */}
        <section className="py-20 bg-[#FAFAFA]">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-[#004E64] mb-4">
                Why Get a Free Case Review?
              </h2>
              <p className="text-xl text-[#999] max-w-3xl mx-auto">
                Understanding your legal options is the first step toward getting the compensation you deserve.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-xl shadow-sm text-center"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <benefit.icon className="h-8 w-8 text-blue-800" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* What to Expect Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <h2 className="text-4xl font-bold text-gray-900">
                  What to Expect During Your Free Consultation
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Our free case review is designed to help you understand your legal rights and options 
                  without any pressure or obligation. Here's what you can expect:
                </p>
                
                <div className="space-y-4">
                  {whatToExpect.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="h-6 w-6 text-amber-600 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </motion.div>
                  ))}
                </div>

                <div className="bg-amber-50 p-6 rounded-xl border border-amber-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <Shield className="h-6 w-6 text-amber-600" />
                    <h3 className="font-bold text-gray-900">Your Information is Protected</h3>
                  </div>
                  <p className="text-gray-700">
                    Everything you tell us during your consultation is confidential and protected by attorney-client privilege, 
                    even if you decide not to hire us.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <img
                  src="https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=600&h=600&fit=crop&crop=center"
                  alt="Attorney consultation meeting"
                  className="rounded-2xl shadow-xl w-full h-96 object-cover"
                />
                <div className="absolute -bottom-6 -left-6 bg-blue-900 text-white p-6 rounded-xl shadow-xl">
                  <div className="text-center">
                    <div className="text-3xl font-bold mb-1">24/7</div>
                    <div className="text-sm">Available</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Consultation Form Section */}
        <section id="consultation-form" className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Start Your Free Case Review Now
              </h2>
              <p className="text-xl text-gray-600">
                Fill out the form below or call us directly. We'll get back to you within 24 hours.
              </p>
            </motion.div>

            <ConsultationForm />
          </div>
        </section>

        {/* Emergency Contact Section */}
        <section className="py-20 bg-red-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Emergency? We're Here 24/7</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                If you or a loved one has been seriously injured, don't wait. Call us now for immediate legal assistance.
              </p>
              <a
                href="tel:+1234567890"
                className="inline-flex items-center justify-center bg-white text-red-900 px-12 py-6 rounded-lg text-2xl font-bold hover:bg-gray-100 transition-colors duration-200 shadow-lg"
              >
                <Phone className="h-8 w-8 mr-4" />
                Emergency Line: (*************
              </a>
              <p className="text-sm text-gray-300 mt-4">
                Available 24 hours a day, 7 days a week for emergency consultations
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default FreeReview;