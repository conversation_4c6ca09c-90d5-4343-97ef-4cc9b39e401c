import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Car, 
  Truck, 
  Activity, 
  Heart, 
  Shield, 
  Phone,
  CheckCircle,
  Award,
  TrendingUp
} from 'lucide-react';
import SEO from '../components/SEO';
import HeroSection from '../components/HeroSection';
import PracticeAreaCard from '../components/PracticeAreaCard';
import TestimonialCard from '../components/TestimonialCard';
import ConsultationForm from '../components/ConsultationForm';

const Home: React.FC = () => {
  const practiceAreas = [
    {
      icon: Car,
      title: 'Car Accidents',
      description: 'Experienced representation for auto accident victims. We handle insurance claims, medical bills, and fight for maximum compensation.'
    },
    {
      icon: Truck,
      title: 'Truck Accidents', 
      description: 'Commercial truck accidents require specialized knowledge. Our team understands federal regulations and liability issues.'
    },
    {
      icon: Activity,
      title: 'Slip & Fall',
      description: 'Property owners have a duty to maintain safe premises. We prove negligence and recover damages for slip and fall injuries.'
    },
    {
      icon: Heart,
      title: 'Medical Malpractice',
      description: 'When medical professionals fail to meet standards of care, we hold them accountable for preventable injuries.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Miami, FL',
      rating: 5,
      testimonial: 'After my car accident, I was overwhelmed with medical bills and insurance companies. This team fought for me every step of the way and got me the compensation I deserved.',
      caseType: 'Car Accident',
      index: 0
    },
    {
      name: 'James Thompson',
      location: 'Orlando, FL',
      rating: 5,
      testimonial: 'Professional, compassionate, and results-driven. They recovered $450,000 for my truck accident case. I would not hesitate to recommend them to anyone.',
      caseType: 'Truck Accident', 
      index: 1
    },
    {
      name: 'Sarah Johnson',
      location: 'Tampa, FL',
      rating: 5,
      testimonial: 'The attorneys were incredibly knowledgeable about slip and fall cases. They handled everything while I focused on recovery. Excellent communication throughout.',
      caseType: 'Slip & Fall',
      index: 2
    }
  ];

  const whyChooseUs = [
    { icon: Shield, title: 'No Win, No Fee', description: 'We only get paid when you do' },
    { icon: Award, title: '25+ Years Experience', description: 'Decades of proven results' },
    { icon: TrendingUp, title: '$100M+ Recovered', description: 'Maximum compensation for clients' },
    { icon: Phone, title: '24/7 Availability', description: 'Always here when you need us' }
  ];

  return (
    <>
      <SEO 
        title="Florida Accident Lawyer - Personal Injury Attorneys"
        description="Experienced Florida personal injury lawyers fighting for accident victims. Free consultation. No win, no fee. Over $100M recovered for clients across Florida."
        keywords="Florida accident lawyer, personal injury attorney, car accident lawyer, truck accident, slip and fall, medical malpractice"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <HeroSection />

        {/* Practice Areas Section */}
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-20"
            >
              <h2 className="text-5xl font-bold text-gray-900 mb-6">
                We Handle All Types of Accident Cases
              </h2>
              <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Our experienced attorneys have recovered over $100 million for Florida accident victims. 
                We fight insurance companies so you can focus on healing.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {practiceAreas.map((area, index) => (
                <PracticeAreaCard
                  key={area.title}
                  icon={area.icon}
                  title={area.title}
                  description={area.description}
                  index={index}
                />
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mt-12"
            >
              <Link
                to="/practice-areas"
                className="inline-flex items-center bg-blue-800 text-white px-10 py-4 rounded-lg font-bold text-lg hover:bg-blue-900 transition-all duration-200 shadow-lg transform hover:scale-105"
              >
                View All Practice Areas
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-24 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-5xl font-bold mb-6">
                Proven Results for Our Clients
              </h2>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto">
                We don't just talk about results - we deliver them. Here are some of our recent victories.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { amount: '$2.3M', case: 'Car Accident Settlement' },
                { amount: '$1.8M', case: 'Truck Accident Verdict' },
                { amount: '$950K', case: 'Slip & Fall Settlement' },
                { amount: '$1.2M', case: 'Medical Malpractice' }
              ].map((result, index) => (
                <motion.div
                  key={result.case}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center bg-white/10 backdrop-blur-sm rounded-xl p-8"
                >
                  <div className="text-4xl font-bold text-amber-400 mb-2">{result.amount}</div>
                  <div className="text-gray-200">{result.case}</div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mt-12"
            >
              <p className="text-gray-300 mb-6">
                *Past results do not guarantee future outcomes. Every case is unique.
              </p>
              <Link
                to="/free-case-review"
                className="inline-flex items-center bg-amber-600 text-white px-10 py-4 rounded-lg font-bold text-lg hover:bg-amber-700 transition-all duration-200 shadow-lg transform hover:scale-105"
              >
                Get Your Free Case Review
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-20"
            >
              <h2 className="text-5xl font-bold text-gray-900 mb-6">
                Why Choose Florida Accident Lawyer?
              </h2>
              <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                When you're injured, you need more than just a lawyer - you need a fighter. 
                Here's what sets us apart from other law firms.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
              {whyChooseUs.map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center bg-gray-50 p-8 rounded-xl"
                >
                  <div className="inline-flex items-center justify-center w-24 h-24 bg-amber-100 rounded-full mb-6">
                    <item.icon className="h-12 w-12 text-amber-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{item.title}</h3>
                  <p className="text-gray-600 text-lg leading-relaxed">{item.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                What Our Clients Say
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Real results from real clients who trusted us with their cases.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial) => (
                <TestimonialCard
                  key={testimonial.name}
                  {...testimonial}
                />
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mt-12"
            >
              <Link
                to="/testimonials"
                className="inline-flex items-center bg-amber-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
              >
                Read More Testimonials
              </Link>
            </motion.div>
          </div>
        </section>

        {/* CTA Section with Form */}
        <section className="py-20 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <h2 className="text-4xl font-bold">
                  Ready to Get Started?
                </h2>
                <p className="text-xl text-gray-200">
                  Don't wait to get the legal help you need. The sooner you contact us, the better we can protect your rights and build your case.
                </p>
                
                <div className="space-y-4">
                  {[
                    'Free, no-obligation consultation',
                    'We handle all paperwork and negotiations',
                    'Available 24/7 for emergencies',
                    'Contingency fee - no upfront costs'
                  ].map((benefit, index) => (
                    <motion.div
                      key={benefit}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="h-6 w-6 text-amber-400" />
                      <span className="text-lg">{benefit}</span>
                    </motion.div>
                  ))}
                </div>

                <div className="pt-6">
                  <a
                    href="tel:+1234567890"
                    className="inline-flex items-center bg-amber-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
                  >
                    <Phone className="h-6 w-6 mr-3" />
                    Call Now: (*************
                  </a>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <ConsultationForm />
              </motion.div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;