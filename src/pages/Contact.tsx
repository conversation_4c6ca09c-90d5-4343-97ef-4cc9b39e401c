import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Mail, MapPin, Clock, Car, Users, Award } from 'lucide-react';
import SEO from '../components/SEO';
import ConsultationForm from '../components/ConsultationForm';

const Contact: React.FC = () => {
  const contactInfo = [
    {
      icon: Phone,
      title: 'Phone Numbers',
      details: [
        'Main Office: (*************',
        'Emergency Line: (*************',
        'Toll Free: (*************'
      ]
    },
    {
      icon: Mail,
      title: 'Email Addresses',
      details: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]
    },
    {
      icon: MapPin,
      title: 'Office Locations',
      details: [
        'Miami: 123 Legal Way, Miami, FL 33101',
        'Orlando: 456 Law Ave, Orlando, FL 32801',
        'Tampa: 789 Justice Blvd, Tampa, FL 33601'
      ]
    },
    {
      icon: Clock,
      title: 'Office Hours',
      details: [
        'Monday - Friday: 8:00 AM - 6:00 PM',
        'Saturday: 9:00 AM - 2:00 PM',
        'Sunday: Emergency Calls Only'
      ]
    }
  ];

  const offices = [
    {
      city: 'Miami',
      address: '123 Legal Way, Suite 500',
      cityState: 'Miami, FL 33101',
      phone: '(*************',
      description: 'Our main office serving Miami-Dade County and surrounding areas.'
    },
    {
      city: 'Orlando',
      address: '456 Law Avenue, Floor 3',
      cityState: 'Orlando, FL 32801', 
      phone: '(*************',
      description: 'Serving Central Florida including Orange, Seminole, and Osceola counties.'
    },
    {
      city: 'Tampa',
      address: '789 Justice Boulevard',
      cityState: 'Tampa, FL 33601',
      phone: '(*************',
      description: 'Covering Tampa Bay area including Hillsborough, Pinellas, and Pasco counties.'
    }
  ];

  return (
    <>
      <SEO 
        title="Contact Florida Accident Lawyer - Personal Injury Law Firm"
        description="Contact experienced Florida personal injury attorneys. Multiple office locations, 24/7 emergency line. Free consultation available. Call (*************."
        keywords="contact Florida accident lawyer, personal injury attorney contact, law firm locations, legal consultation"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-5xl font-bold mb-6">Contact Us Today</h1>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto mb-8">
                Ready to get the legal help you need? We have multiple offices throughout Florida 
                and are available 24/7 for emergency consultations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:+1234567890"
                  className="inline-flex items-center justify-center bg-amber-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
                >
                  <Phone className="h-6 w-6 mr-3" />
                  Call Now: (*************
                </a>
                <a
                  href="#contact-form"
                  className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors duration-200"
                >
                  Get Free Consultation
                </a>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Quick Contact Info */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={info.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <info.icon className="h-8 w-8 text-blue-800" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{info.title}</h3>
                  <div className="space-y-1">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-gray-600 text-sm">{detail}</p>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Office Locations */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Office Locations</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                With offices throughout Florida, we're conveniently located to serve you better.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {offices.map((office, index) => (
                <motion.div
                  key={office.city}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg p-8"
                >
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{office.city} Office</h3>
                    <div className="w-16 h-1 bg-amber-600 mx-auto"></div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-blue-800 mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-semibold text-gray-900">{office.address}</p>
                        <p className="text-gray-600">{office.cityState}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-blue-800" />
                      <a href={`tel:${office.phone}`} className="text-blue-800 font-semibold hover:text-blue-900">
                        {office.phone}
                      </a>
                    </div>
                    
                    <p className="text-gray-600 text-sm leading-relaxed">{office.description}</p>
                  </div>
                  
                  <div className="mt-6 pt-6 border-t border-gray-100">
                    <a
                      href={`https://maps.google.com/?q=${encodeURIComponent(office.address + ', ' + office.cityState)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-amber-600 font-semibold hover:text-amber-700 transition-colors duration-200"
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      View on Map
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Map Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Find Us on the Map</h2>
              <p className="text-xl text-gray-600">
                Visit any of our convenient office locations throughout Florida.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gray-200 rounded-2xl h-96 flex items-center justify-center"
            >
              <div className="text-center">
                <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 text-lg">Interactive Map Placeholder</p>
                <p className="text-gray-500 text-sm mt-2">
                  In a real implementation, this would show an embedded Google Map
                  <br />
                  with our office locations marked
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Contact Form Section */}
        <section id="contact-form" className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-start">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="space-y-8"
              >
                <div>
                  <h2 className="text-4xl font-bold text-gray-900 mb-4">
                    Get Your Free Consultation
                  </h2>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Don't wait to get the legal help you need. Fill out our contact form or give us a call, 
                    and we'll get back to you within 24 hours to discuss your case.
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                      <Car className="h-6 w-6 text-blue-800" />
                    </div>
                    <h3 className="font-bold text-gray-900">Car Accidents</h3>
                    <p className="text-sm text-gray-600">2,500+ Cases</p>
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-amber-100 rounded-full mb-3">
                      <Users className="h-6 w-6 text-amber-600" />
                    </div>
                    <h3 className="font-bold text-gray-900">Clients Served</h3>
                    <p className="text-sm text-gray-600">5,000+</p>
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                      <Award className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="font-bold text-gray-900">Success Rate</h3>
                    <p className="text-sm text-gray-600">95%+</p>
                  </div>
                </div>

                <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                  <h3 className="font-bold text-gray-900 mb-3">Why Choose Us?</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• No win, no fee guarantee</li>
                    <li>• 24/7 emergency availability</li>
                    <li>• Over $100M recovered for clients</li>
                    <li>• Free case evaluation</li>
                    <li>• Experienced trial attorneys</li>
                  </ul>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <ConsultationForm />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Emergency Contact Section */}
        <section className="py-20 bg-red-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Have an Emergency?</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                If you or a loved one has been seriously injured in an accident, don't wait. 
                Call our emergency line now for immediate legal assistance.
              </p>
              <a
                href="tel:+1234567890"
                className="inline-flex items-center justify-center bg-white text-red-900 px-12 py-6 rounded-lg text-2xl font-bold hover:bg-gray-100 transition-colors duration-200 shadow-lg"
              >
                <Phone className="h-8 w-8 mr-4" />
                Emergency Line: (*************
              </a>
              <p className="text-sm text-gray-300 mt-4">
                Available 24 hours a day, 7 days a week
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Contact;