import React from 'react';
import { motion } from 'framer-motion';
import { Award, Users, TrendingUp, Shield, Phone, Heart } from 'lucide-react';
import SEO from '../components/SEO';

const About: React.FC = () => {
  const stats = [
    { icon: Award, label: 'Years of Experience', value: '25+' },
    { icon: Users, label: 'Clients Served', value: '5,000+' },
    { icon: TrendingUp, label: 'Cases Won', value: '95%' },
    { icon: Shield, label: 'Total Recovered', value: '$100M+' }
  ];

  const values = [
    {
      title: 'Client-Focused',
      description: 'Every decision we make is centered on what\'s best for our clients and their families.'
    },
    {
      title: 'Experienced',
      description: 'With over 25 years of combined experience, we know how to navigate complex legal challenges.'
    },
    {
      title: 'Results-Driven',
      description: 'We fight aggressively to secure maximum compensation for every client we represent.'
    },
    {
      title: 'Accessible',
      description: 'We\'re available 24/7 because accidents don\'t happen on a schedule.'
    }
  ];

  return (
    <>
      <SEO 
        title="About Florida Accident Lawyer - Experienced Personal Injury Attorneys"
        description="Learn about our experienced personal injury law team in Florida. Over 25 years fighting for accident victims with $100M+ recovered for our clients."
        keywords="about Florida accident lawyer, personal injury attorneys, experienced lawyers, Florida law firm"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-5xl font-bold mb-6">About Our Firm</h1>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto">
                For over two decades, we've been fighting for the rights of accident victims throughout Florida, 
                securing life-changing compensation for thousands of clients.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-4">
                    <stat.icon className="h-8 w-8 text-amber-600" />
                  </div>
                  <div className="text-3xl font-bold text-blue-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <h2 className="text-4xl font-bold text-gray-900">Our Story</h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Florida Accident Lawyer was founded with a simple mission: to provide exceptional legal 
                  representation to accident victims throughout the state of Florida. Our founders recognized 
                  that many injured people were being taken advantage of by insurance companies and needed 
                  experienced advocates to fight for their rights.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Since our founding, we've grown into one of Florida's most trusted personal injury law firms, 
                  but we've never forgotten our roots. We still treat every client like family and fight for 
                  every case as if it were our own.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Today, we're proud to have recovered over $100 million for our clients and helped thousands 
                  of families get back on their feet after devastating accidents.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=600&fit=crop&crop=center"
                  alt="Lead Attorney"
                  className="rounded-2xl shadow-xl w-full h-96 object-cover"
                />
                <div className="absolute -bottom-6 -left-6 bg-amber-600 text-white p-6 rounded-xl shadow-xl">
                  <div className="flex items-center space-x-3">
                    <Heart className="h-8 w-8" />
                    <div>
                      <p className="font-bold text-lg">Michael Rodriguez</p>
                      <p className="text-sm">Lead Attorney & Founder</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Our Values Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These core principles guide everything we do and every decision we make for our clients.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl text-center"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Attorney Profile Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Lead Attorney</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Experienced. Dedicated. Results-driven.
              </p>
            </motion.div>

            <div className="grid lg:grid-cols-3 gap-12 items-start">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="lg:col-span-1"
              >
                <img
                  src="https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=500&fit=crop&crop=center"
                  alt="Michael Rodriguez, Lead Attorney"
                  className="rounded-2xl shadow-xl w-full h-96 object-cover mb-6"
                />
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Michael Rodriguez</h3>
                  <p className="text-amber-600 font-semibold mb-4">Lead Attorney & Founder</p>
                  <p className="text-gray-600 text-sm">
                    J.D., University of Florida Law School<br />
                    Licensed in Florida since 1999
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="lg:col-span-2 space-y-6"
              >
                <div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Experience & Education</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Michael Rodriguez has been fighting for accident victims in Florida for over 25 years. 
                    A graduate of the University of Florida Law School, Michael started his career at a large 
                    insurance defense firm, where he learned firsthand how insurance companies operate and 
                    how to beat them at their own game.
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    This insider knowledge has proven invaluable in securing maximum compensation for his clients. 
                    Michael has personally handled over 3,000 personal injury cases and has recovered over 
                    $100 million for his clients.
                  </p>
                </div>

                <div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Recognition & Awards</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Super Lawyers Rising Star (2005-2010)</li>
                    <li>• Florida Super Lawyers (2011-2024)</li>
                    <li>• Martindale-Hubbell AV Preeminent Rating</li>
                    <li>• Best Lawyers in America - Personal Injury Litigation</li>
                    <li>• Florida Bar Board Certified in Civil Trial Law</li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Community Involvement</h4>
                  <p className="text-gray-700 leading-relaxed">
                    When not fighting for his clients, Michael is actively involved in the community. 
                    He serves on the board of several local charities and regularly speaks at safety 
                    seminars throughout Florida. Michael believes in giving back to the community 
                    that has supported his practice for over two decades.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Ready to Get the Help You Need?</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                Don't face the insurance companies alone. Let our experienced team fight for the compensation you deserve.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:+1234567890"
                  className="inline-flex items-center justify-center bg-amber-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
                >
                  <Phone className="h-6 w-6 mr-3" />
                  Call Now: (*************
                </a>
                <a
                  href="/free-case-review"
                  className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors duration-200"
                >
                  Free Case Review
                </a>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default About;