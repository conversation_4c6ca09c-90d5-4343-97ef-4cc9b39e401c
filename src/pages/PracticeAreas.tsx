import React from 'react';
import { motion } from 'framer-motion';
import { 
  Car, 
  Truck, 
  Activity, 
  Heart, 
  Building, 
  Zap,
  Users,
  Shield,
  Phone,
  CheckCircle
} from 'lucide-react';
import SEO from '../components/SEO';

const PracticeAreas: React.FC = () => {
  const practiceAreas = [
    {
      icon: Car,
      title: 'Car Accidents',
      description: 'Comprehensive representation for all types of motor vehicle accidents including rear-end collisions, intersection accidents, and DUI crashes.',
      details: [
        'Rear-end collisions',
        'Intersection accidents', 
        'Head-on collisions',
        'Hit and run accidents',
        'Drunk driving accidents',
        'Distracted driving cases'
      ],
      stats: '2,500+ Cases Won'
    },
    {
      icon: Truck,
      title: 'Truck Accidents',
      description: 'Specialized knowledge in commercial trucking regulations and the complex liability issues involved in 18-wheeler accidents.',
      details: [
        'Semi-truck collisions',
        'Delivery truck accidents',
        'Commercial vehicle crashes',
        'Trucking company negligence',
        'Driver fatigue cases',
        'Improper loading accidents'
      ],
      stats: '$45M+ Recovered'
    },
    {
      icon: Activity,
      title: 'Slip & Fall',
      description: 'Premises liability cases where property owners failed to maintain safe conditions, resulting in serious injuries.',
      details: [
        'Wet floor accidents',
        'Uneven surfaces',
        'Poor lighting conditions',
        'Defective stairs/railings',
        'Ice and snow hazards',
        'Inadequate security'
      ],
      stats: '1,200+ Claims Settled'
    },
    {
      icon: Heart,
      title: 'Medical Malpractice',
      description: 'Holding healthcare professionals accountable when medical errors result in preventable injuries or death.',
      details: [
        'Surgical errors',
        'Misdiagnosis/delayed diagnosis',
        'Medication errors',
        'Birth injuries',
        'Hospital negligence',
        'Emergency room mistakes'
      ],
      stats: '300+ Medical Cases'
    },
    {
      icon: Building,
      title: 'Construction Accidents',
      description: 'Workplace injuries on construction sites, including falls, equipment accidents, and unsafe working conditions.',
      details: [
        'Scaffolding accidents',
        'Fall from height',
        'Equipment malfunctions',
        'Electrocution injuries',
        'Crane accidents',
        'Site safety violations'
      ],
      stats: '95% Success Rate'
    },
    {
      icon: Users,
      title: 'Wrongful Death',
      description: 'Compassionate representation for families who have lost loved ones due to someone else\'s negligence.',
      details: [
        'Fatal car accidents',
        'Medical malpractice deaths',
        'Workplace fatalities',
        'Defective products',
        'Nursing home neglect',
        'Criminal acts'
      ],
      stats: '$25M+ for Families'
    },
    {
      icon: Zap,
      title: 'Product Liability',
      description: 'Cases involving defective or dangerous products that cause injury due to design flaws or manufacturing defects.',
      details: [
        'Defective medical devices',
        'Dangerous pharmaceuticals',
        'Auto defects/recalls',
        'Consumer products',
        'Industrial equipment',
        'Children\'s products'
      ],
      stats: '500+ Product Cases'
    },
    {
      icon: Shield,
      title: 'Insurance Bad Faith',
      description: 'When insurance companies unfairly deny, delay, or underpay valid claims, we hold them accountable.',
      details: [
        'Claim denials',
        'Unreasonable delays',
        'Lowball settlements',
        'Policy interpretation',
        'Coverage disputes',
        'Unfair investigations'
      ],
      stats: 'Millions Recovered'
    }
  ];

  return (
    <>
      <SEO 
        title="Practice Areas - Florida Personal Injury Law Services"
        description="Comprehensive personal injury legal services in Florida. Car accidents, truck accidents, slip & fall, medical malpractice, wrongful death, and more."
        keywords="Florida personal injury practice areas, car accident lawyer, truck accident attorney, slip and fall, medical malpractice, wrongful death"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-5xl font-bold mb-6">Our Practice Areas</h1>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto">
                We specialize in all types of personal injury cases throughout Florida. 
                No matter how complex your case, we have the experience to help you win.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Practice Areas Grid */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-12">
              {practiceAreas.map((area, index) => (
                <motion.div
                  key={area.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                >
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="flex-shrink-0">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                        <area.icon className="h-8 w-8 text-blue-800" />
                      </div>
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{area.title}</h3>
                      <div className="inline-block bg-amber-100 text-amber-800 text-sm px-3 py-1 rounded-full font-medium">
                        {area.stats}
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-600 leading-relaxed mb-6">
                    {area.description}
                  </p>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 mb-3">We Handle:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {area.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-amber-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{detail}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Why Choose Our Firm?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our track record speaks for itself. We have the experience, resources, and dedication 
                to handle the most complex personal injury cases.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Proven Track Record',
                  description: 'Over $100M recovered for clients with a 95% success rate in personal injury cases.'
                },
                {
                  title: 'No Win, No Fee',
                  description: 'We work on contingency, so you pay nothing unless we win your case.'
                },
                {
                  title: 'Experienced Team',
                  description: '25+ years of combined experience handling complex personal injury matters.'
                },
                {
                  title: 'Personalized Service',
                  description: 'Direct access to your attorney and personalized attention throughout your case.'
                },
                {
                  title: 'Maximum Compensation',
                  description: 'We fight aggressively to secure the full compensation you deserve.'
                },
                {
                  title: '24/7 Availability',
                  description: 'Available around the clock because accidents don\'t happen on schedule.'
                }
              ].map((benefit, index) => (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-lg shadow-sm"
                >
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Been Injured? We Can Help.</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                Don't let insurance companies take advantage of you. Get experienced legal representation 
                that will fight for every dollar you deserve.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:+1234567890"
                  className="inline-flex items-center justify-center bg-amber-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
                >
                  <Phone className="h-6 w-6 mr-3" />
                  Call Now: (*************
                </a>
                <a
                  href="/free-case-review"
                  className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors duration-200"
                >
                  Free Case Review
                </a>
              </div>
              <p className="text-sm text-gray-300 mt-4">
                Free consultation • No upfront fees • Available 24/7
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default PracticeAreas;