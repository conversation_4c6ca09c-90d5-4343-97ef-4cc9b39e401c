import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote, Phone, Award } from 'lucide-react';
import SEO from '../components/SEO';
import TestimonialCard from '../components/TestimonialCard';

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Miami, FL',
      rating: 5,
      testimonial: 'After my car accident, I was overwhelmed with medical bills and insurance companies giving me the runaround. The team at Florida Accident Lawyer fought for me every step of the way and got me the compensation I deserved. I couldn\'t be more grateful.',
      caseType: 'Car Accident',
      index: 0
    },
    {
      name: '<PERSON>',
      location: 'Orlando, FL', 
      rating: 5,
      testimonial: 'Professional, compassionate, and results-driven. They recovered $450,000 for my truck accident case when the insurance company initially offered only $25,000. I would not hesitate to recommend them to anyone.',
      caseType: 'Truck Accident',
      index: 1
    },
    {
      name: '<PERSON>',
      location: 'Tampa, FL',
      rating: 5,
      testimonial: 'The attorneys were incredibly knowledgeable about slip and fall cases. They handled everything while I focused on recovery. Excellent communication throughout the process and achieved a fantastic result.',
      caseType: 'Slip & Fall',
      index: 2
    },
    {
      name: '<PERSON>',
      location: 'Jacksonville, FL',
      rating: 5,
      testimonial: 'When my wife died in a medical malpractice case, I thought we had no options. This firm took on the hospital and secured a $2.3M settlement that will help care for our children. They treated us with dignity during the worst time of our lives.',
      caseType: 'Medical Malpractice',
      index: 3
    },
    {
      name: 'Linda Martinez',
      location: 'Fort Lauderdale, FL',
      rating: 5,
      testimonial: 'I was injured in a construction accident and didn\'t think I had a case since I was an independent contractor. They fought the system and got me $380,000 for my injuries. True professionals who care about their clients.',
      caseType: 'Construction Accident',
      index: 4
    },
    {
      name: 'David Wilson',
      location: 'St. Petersburg, FL',
      rating: 5,
      testimonial: 'The insurance company was acting in bad faith, delaying my claim for months. This firm stepped in and not only got my claim paid but also secured additional damages for their bad faith conduct. Highly recommend!',
      caseType: 'Insurance Bad Faith',
      index: 5
    },
    {
      name: 'Angela Foster',
      location: 'Gainesville, FL',
      rating: 5,
      testimonial: 'After a defective product injured my daughter, we needed experienced attorneys who understood product liability law. They took on the manufacturer and secured a settlement that will cover her medical needs for life.',
      caseType: 'Product Liability',
      index: 6
    },
    {
      name: 'Michael Brown',
      location: 'Pensacola, FL',
      rating: 5,
      testimonial: 'I was rear-ended by a distracted driver and suffered serious back injuries. The team was professional, kept me informed, and fought hard to get me maximum compensation. They truly care about their clients.',
      caseType: 'Car Accident',
      index: 7
    },
    {
      name: 'Jennifer Davis',
      location: 'Tallahassee, FL',
      rating: 5,
      testimonial: 'When my husband was killed by a drunk driver, I didn\'t know where to turn. This firm guided me through the legal process with compassion and secured justice for our family. I can\'t thank them enough.',
      caseType: 'Wrongful Death',
      index: 8
    }
  ];

  const stats = [
    { value: '500+', label: 'Five-Star Reviews' },
    { value: '98%', label: 'Client Satisfaction' }, 
    { value: '5,000+', label: 'Clients Served' },
    { value: '$100M+', label: 'Total Recovered' }
  ];

  return (
    <>
      <SEO 
        title="Client Testimonials - Florida Accident Lawyer Reviews"
        description="Read real testimonials from satisfied clients of Florida Accident Lawyer. See why we have 500+ five-star reviews and 98% client satisfaction rate."
        keywords="Florida accident lawyer reviews, personal injury attorney testimonials, client satisfaction, lawyer reviews"
      />

      <div className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-8 w-8 text-amber-400 fill-current" />
                  ))}
                </div>
              </div>
              <h1 className="text-5xl font-bold mb-6">What Our Clients Say</h1>
              <p className="text-xl text-gray-200 max-w-3xl mx-auto">
                Don't just take our word for it. Hear from real clients who we've helped recover 
                millions in compensation after serious accidents.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-4xl font-bold text-blue-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Grid */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Real Stories, Real Results</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These are just a few of the thousands of clients we've helped get their lives back on track 
                after devastating accidents.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial) => (
                <TestimonialCard
                  key={testimonial.name}
                  {...testimonial}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Google Reviews Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-8">
                Consistently 5-Star Rated
              </h2>
              
              <div className="bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto">
                <div className="flex items-center justify-center mb-6">
                  <img 
                    src="https://upload.wikimedia.org/wikipedia/commons/5/53/Google_%22G%22_Logo.svg" 
                    alt="Google" 
                    className="h-8 w-8 mr-3"
                  />
                  <span className="text-2xl font-bold text-gray-900">Google Reviews</span>
                </div>
                
                <div className="flex justify-center mb-4">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-8 w-8 text-amber-400 fill-current" />
                    ))}
                  </div>
                </div>
                
                <div className="text-3xl font-bold text-gray-900 mb-2">4.9 out of 5.0</div>
                <div className="text-gray-600 mb-6">Based on 500+ verified Google reviews</div>
                
                <blockquote className="text-lg italic text-gray-700 mb-6">
                  "Consistently rated as one of Florida's top personal injury law firms by our clients. 
                  We're proud of our track record and commitment to excellence."
                </blockquote>
                
                <div className="flex items-center justify-center">
                  <Award className="h-6 w-6 text-amber-600 mr-2" />
                  <span className="text-amber-600 font-semibold">Google Guaranteed Business</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Ready to Become Our Next Success Story?</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                Join thousands of satisfied clients who chose Florida Accident Lawyer 
                for their personal injury case. Get your free consultation today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:+1234567890"
                  className="inline-flex items-center justify-center bg-amber-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-amber-700 transition-colors duration-200"
                >
                  <Phone className="h-6 w-6 mr-3" />
                  Call Now: (*************
                </a>
                <a
                  href="/free-case-review"
                  className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors duration-200"
                >
                  Get Free Case Review
                </a>
              </div>
              <p className="text-sm text-gray-300 mt-4">
                No win, no fee • Free consultation • 24/7 availability
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Testimonials;